/*打包session的json结构*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <time.h>

#include "dpi_arkime_session.h"
#include "dpi_arkime_es.h"
#include "../dpi_log.h"
#include "../dpi_detect.h"
#include "../dpi_tbl_log.h"
#include "cJSON.h"

extern struct global_config g_config;

// 从flow创建会话JSON数据
char* dpi_arkime_create_session_json_from_flow(struct flow_info *flow) {
    // 检查ES是否启用
    if (!g_config.es_config.enabled) {
        return NULL; // ES未启用时返回NULL
    }

    if (!flow) {
        return NULL;
    }

    cJSON *session = cJSON_CreateObject();
    if (!session) {
        return NULL;
    }

    // 生成会话ID（使用flow_id）
    char session_id[64];
    snprintf(session_id, sizeof(session_id), "%lu", flow->flow_id);
    cJSON_AddStringToObject(session, "sessionId", session_id);

    // 添加基本会话信息
    cJSON_AddNumberToObject(session, "firstPacket", flow->create_time);
    cJSON_AddNumberToObject(session, "lastPacket", flow->last_seen);
    cJSON_AddNumberToObject(session, "bytes", flow->src2dst_bytes + flow->dst2src_bytes);
    cJSON_AddNumberToObject(session, "packets", flow->src2dst_packets + flow->dst2src_packets);

    // 提取IP地址和端口信息
    char src_ip[INET6_ADDRSTRLEN];
    char dst_ip[INET6_ADDRSTRLEN];

    if (flow->ip_version == 4) {
        inet_ntop(AF_INET, flow->tuple.inner.ip_src, src_ip, sizeof(src_ip));
        inet_ntop(AF_INET, flow->tuple.inner.ip_dst, dst_ip, sizeof(dst_ip));
    } else {
        inet_ntop(AF_INET6, flow->tuple.inner.ip_src, src_ip, sizeof(src_ip));
        inet_ntop(AF_INET6, flow->tuple.inner.ip_dst, dst_ip, sizeof(dst_ip));
    }

    cJSON_AddStringToObject(session, "srcIp", src_ip);
    cJSON_AddStringToObject(session, "dstIp", dst_ip);
    cJSON_AddNumberToObject(session, "srcPort", flow->tuple.inner.port_src);
    cJSON_AddNumberToObject(session, "dstPort", flow->tuple.inner.port_dst);
    cJSON_AddNumberToObject(session, "protocol", flow->tuple.inner.proto);

    // 文件位置信息数组 - 从flow的链表中获取
    cJSON *file_pos_array = cJSON_CreateArray();
    cJSON *file_num_array = cJSON_CreateArray();

    // 获取链表数据并转换为数组格式
    int64_t *pos_array = NULL;
    int array_size = 0;

    if (arkime_flow_get_file_pos_array(flow, &pos_array, &array_size) == 0 && pos_array) {
        for (int i = 0; i < array_size; i++) {
            cJSON_AddItemToArray(file_pos_array, cJSON_CreateNumber(pos_array[i]));
        }
        free(pos_array);  // 释放临时数组
    }

    // 提取文件号数组（从链表中的负值提取）
    struct arkime_file_pos_node *current = flow->arkime_file_pos_list;
    while (current) {
        if (current->value < 0) {  // 负值表示文件号
            uint64_t file_num = (uint64_t)(-current->value);
            cJSON_AddItemToArray(file_num_array, cJSON_CreateNumber(file_num));
        }
        current = current->next;
    }

    cJSON_AddItemToObject(session, "filePosArray", file_pos_array);
    cJSON_AddItemToObject(session, "fileNumArray", file_num_array);

    // 节点信息
    cJSON_AddStringToObject(session, "node", g_config.es_config.node_name);

    char *json_string = cJSON_PrintUnformatted(session);
    cJSON_Delete(session);

    return json_string;
}

// 从record创建会话JSON数据 - 完全基于record，因为flow可能已失效
char* dpi_arkime_create_session_json_from_record(precord_t *record) {
    // 检查ES是否启用
    if (!g_config.es_config.enabled) {
        return NULL; // ES未启用时返回NULL
    }

    if (!record || !record->flow) {
        DPI_LOG(DPI_LOG_ERROR, "Invalid record or flow pointer");
        return NULL;
    }

    struct flow_info *flow = record->flow;
    cJSON *session = cJSON_CreateObject();
    if (!session) {
        return NULL;
    }

    // 生成会话ID（使用flow_id）
    char session_id[64];
    snprintf(session_id, sizeof(session_id), "%lu", flow->flow_id);
    cJSON_AddStringToObject(session, "sessionId", session_id);

    // 添加基本会话信息（从flow获取，因为这些是会话级别的统计）
    cJSON_AddNumberToObject(session, "firstPacket", flow->create_time);
    cJSON_AddNumberToObject(session, "lastPacket", flow->last_seen);
    cJSON_AddNumberToObject(session, "bytes", flow->src2dst_bytes + flow->dst2src_bytes);
    cJSON_AddNumberToObject(session, "packets", flow->src2dst_packets + flow->dst2src_packets);

    // 提取IP地址和端口信息（从flow获取）
    char src_ip[INET6_ADDRSTRLEN];
    char dst_ip[INET6_ADDRSTRLEN];

    if (flow->ip_version == 4) {
        inet_ntop(AF_INET, flow->tuple.inner.ip_src, src_ip, sizeof(src_ip));
        inet_ntop(AF_INET, flow->tuple.inner.ip_dst, dst_ip, sizeof(dst_ip));
    } else {
        inet_ntop(AF_INET6, flow->tuple.inner.ip_src, src_ip, sizeof(src_ip));
        inet_ntop(AF_INET6, flow->tuple.inner.ip_dst, dst_ip, sizeof(dst_ip));
    }

    cJSON_AddStringToObject(session, "srcIp", src_ip);
    cJSON_AddStringToObject(session, "dstIp", dst_ip);
    cJSON_AddNumberToObject(session, "srcPort", flow->tuple.inner.port_src);
    cJSON_AddNumberToObject(session, "dstPort", flow->tuple.inner.port_dst);
    cJSON_AddNumberToObject(session, "protocol", flow->tuple.inner.proto);

    // 文件位置信息数组 - 从record的链表中获取（支持多包和跨文件）
    cJSON *file_pos_array = cJSON_CreateArray();
    cJSON *file_num_array = cJSON_CreateArray();

    // 获取链表数据并转换为数组格式
    int64_t *pos_array = NULL;
    int array_size = 0;

    if (arkime_record_get_file_pos_array(record, &pos_array, &array_size) == 0 && pos_array) {
        for (int i = 0; i < array_size; i++) {
            cJSON_AddItemToArray(file_pos_array, cJSON_CreateNumber(pos_array[i]));
        }
        free(pos_array);  // 释放临时数组
    }

    // 提取文件号数组（从链表中的负值提取）
    struct arkime_file_pos_node *current = record->arkime_file_pos_list;
    while (current) {
        if (current->value < 0) {  // 负值表示文件号
            uint64_t file_num = (uint64_t)(-current->value);
            cJSON_AddItemToArray(file_num_array, cJSON_CreateNumber(file_num));
        }
        current = current->next;
    }

    cJSON_AddItemToObject(session, "filePosArray", file_pos_array);
    cJSON_AddItemToObject(session, "fileNumArray", file_num_array);

    // 节点信息
    cJSON_AddStringToObject(session, "node", g_config.es_config.node_name);

    // 将record中的协议字段信息添加到session中
    extern int record_write_json(precord_t *record, cJSON *json);
    record_write_json(record, session);

    char *json_string = cJSON_PrintUnformatted(session);
    cJSON_Delete(session);

    DPI_LOG(DPI_LOG_DEBUG, "Created session JSON from record with file info");

    return json_string;
}

// 发送会话数据到ES - 完全基于record
int dpi_arkime_send_session_to_es(precord_t *record) {
    if (!record || !record->flow || !g_config.es_config.enabled) {
        return 0; // ES未启用或无效record时直接返回成功
    }

    char *session_json = dpi_arkime_create_session_json_from_record(record);
    if (!session_json) {
        DPI_LOG(DPI_LOG_ERROR, "Failed to create session JSON from record");
        return -1;
    }

    int result = dpi_arkime_es_send_session(session_json);
    if (result == 0) {
        DPI_LOG(DPI_LOG_DEBUG, "Sent session data to ES: flow_id=%lu", record->flow->flow_id);
    } else {
        DPI_LOG(DPI_LOG_WARNING, "Failed to send session data to ES: flow_id=%lu", record->flow->flow_id);
    }

    free(session_json);
    return result;
}